/**
 * 测试新的Chat功能
 * 运行命令: node scripts/test-chat-functionality.js
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testChatFunctionality() {
  console.log('🚀 开始测试Chat功能...\n');

  try {
    // 1. 测试数据库连接
    console.log('1. 测试数据库连接...');
    await prisma.$connect();
    console.log('✅ 数据库连接成功\n');

    // 2. 检查数据库表是否存在
    console.log('2. 检查数据库表结构...');
    
    // 检查Chat表
    const chatCount = await prisma.chat.count();
    console.log(`✅ Chat表存在，当前记录数: ${chatCount}`);
    
    // 检查ChatMessage表
    const messageCount = await prisma.chatMessage.count();
    console.log(`✅ ChatMessage表存在，当前记录数: ${messageCount}`);
    
    // 检查Resume表
    const resumeCount = await prisma.resume.count();
    console.log(`✅ Resume表存在，当前记录数: ${resumeCount}\n`);

    // 3. 测试创建用户（如果不存在）
    console.log('3. 检查测试用户...');
    let testUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: '测试用户',
          chatCount: 0,
          isMember: false,
        }
      });
      console.log('✅ 创建测试用户成功');
    } else {
      console.log('✅ 测试用户已存在');
    }

    // 4. 测试创建对话
    console.log('\n4. 测试创建对话...');
    const testChat = await prisma.chat.create({
      data: {
        userId: testUser.id,
        title: '测试对话',
        resumeData: {
          personalInfo: {
            name: '张三',
            email: '<EMAIL>',
            phone: '138-0000-0000',
            linkedin: 'linkedin.com/in/zhangsan'
          },
          summary: '这是一个测试简历',
          experience: [],
          education: [],
          skills: [],
          projects: []
        }
      }
    });
    console.log(`✅ 创建对话成功，ID: ${testChat.id}`);

    // 5. 测试添加消息
    console.log('\n5. 测试添加消息...');
    const userMessage = await prisma.chatMessage.create({
      data: {
        chatId: testChat.id,
        role: 'USER',
        content: '你好，请帮我优化简历'
      }
    });
    console.log(`✅ 添加用户消息成功，ID: ${userMessage.id}`);

    const assistantMessage = await prisma.chatMessage.create({
      data: {
        chatId: testChat.id,
        role: 'ASSISTANT',
        content: '好的，我来帮您优化简历。请告诉我您的工作经验和技能。'
      }
    });
    console.log(`✅ 添加助手消息成功，ID: ${assistantMessage.id}`);

    // 6. 测试查询对话和消息
    console.log('\n6. 测试查询对话和消息...');
    const chatWithMessages = await prisma.chat.findUnique({
      where: { id: testChat.id },
      include: {
        messages: {
          orderBy: { createdAt: 'asc' }
        }
      }
    });

    if (chatWithMessages) {
      console.log(`✅ 查询对话成功，标题: ${chatWithMessages.title}`);
      console.log(`✅ 消息数量: ${chatWithMessages.messages.length}`);
      chatWithMessages.messages.forEach((msg, index) => {
        console.log(`   ${index + 1}. ${msg.role}: ${msg.content.substring(0, 50)}...`);
      });
    }

    // 7. 测试对话搜索
    console.log('\n7. 测试对话搜索...');
    const searchResults = await prisma.chat.findMany({
      where: {
        userId: testUser.id,
        title: {
          contains: '测试'
        }
      }
    });
    console.log(`✅ 搜索结果数量: ${searchResults.length}`);

    // 8. 测试关联查询
    console.log('\n8. 测试关联查询...');
    const userWithChats = await prisma.user.findUnique({
      where: { id: testUser.id },
      include: {
        chats: {
          include: {
            _count: {
              select: { messages: true }
            }
          }
        }
      }
    });

    if (userWithChats) {
      console.log(`✅ 用户对话数量: ${userWithChats.chats.length}`);
      userWithChats.chats.forEach(chat => {
        console.log(`   - ${chat.title} (${chat._count.messages} 条消息)`);
      });
    }

    // 9. 清理测试数据
    console.log('\n9. 清理测试数据...');
    await prisma.chatMessage.deleteMany({
      where: { chatId: testChat.id }
    });
    await prisma.chat.delete({
      where: { id: testChat.id }
    });
    await prisma.user.delete({
      where: { id: testUser.id }
    });
    console.log('✅ 测试数据清理完成');

    console.log('\n🎉 所有测试通过！Chat功能数据库层面工作正常。');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testChatFunctionality();
