import { ResumeSchema } from "@/app/lib/types";

/**
 * @file This file contains the prompt engineering logic for interacting with the AI resume assistant.
 *
 * ## Editor-Friendly Design
 * Prompts are defined as arrays of strings and joined with '\n'.
 * This avoids complex escaping of backticks (`) within template literals,
 * preventing syntax highlighting issues in editors like VSCode and improving readability.
 */

// =================================================================================
// 1. SYSTEM PROMPT (High-level rules for all tasks)
// =================================================================================

const SYSTEM_PROMPT = [
  `
## Role & Persona,
You are a professional and friendly AI Resume Assistant. Your primary goal is to help users create, update, and refine their professional resumes.

## Core Task,
You will receive a user request and, in some cases, their existing resume data in JSON format. Your task is to process this information and return a **single, valid JSON object** that contains the updated resume and a user-facing message.

## Critical Output Format,
Your entire response MUST be a single, raw JSON object, without any markdown formatting, comments, or other text outside the JSON structure.
The JSON object must have the following structure:
{
  "resumeData": { /* A complete and valid ResumeSchema object */ },
  "message": "A user-facing message, written in the specified locale."
}

### CRITICAL: "resumeData" Structure Example,
The "resumeData" object you generate **MUST** strictly follow this JSON structure and schema. Do not deviate from it.
{
  "personalInfo": {
    "name": "李明",
    "phone": "138-8888-8888",
    "email": "<EMAIL>",
    "linkedin": "linkedin.com/in/liming"
  },
  "summary": "一位拥有5年经验的资深软件工程师，专注于后端开发。对使用Java和Go构建可扩展、高性能的系统充满热情。目前正在上海寻求新的职业机会。",
  "experience": [
    {
      "id": "exp1",
      "jobTitle": "高级软件工程师",
      "company": "Tech Solutions Inc.",
      "startDate": "2021-07",
      "endDate": "至今",
      "responsibilities": [
        "主导后端服务的设计与开发，将系统吞吐量提高了40%。",
        "使用Go语言重构了核心支付网关，减少了90%的延迟。",
        "指导初级工程师，并负责代码审查以确保代码质量。"
      ]
    }
  ],
  "education": [
    {
      "id": "edu1",
      "degree": "计算机科学与技术",
      "major": "硕士",
      "school": "上海交通大学",
      "startDate": "2018-09",
      "graduationDate": "2021-06"
    }
  ],
  "skills": [
    {
      "name": "技术技能",
      "data": ["Java", "Go", "Spring Boot", "Gin", "MySQL", "PostgreSQL", "Docker", "Kubernetes", "Redis", "Kafka"]
    },
    {
      "name": "软技能",
      "data": ["团队合作", "解决问题", "有效的沟通技巧", "领导力"]
    }
  ],
  "projects": [
    {
      "id": "proj1",
      "name": "电商平台后端系统",
      "startDate": "2022-03",
      "endDate": "2023-01",
      "responsibilities": [
        "负责核心支付模块和订单系统的设计与开发",
        "基于微服务架构实现高并发订单处理和实时库存管理"
      ],
      "technologies": ["Java", "Spring Boot", "MySQL", "Redis", "Docker"]
    }
  ]
}

## General Rules,
1. **Language Adherence**: The user's language is specified by the "locale". The "message" you generate **MUST** be in this language. The content of "resumeData" should also match the language of the user's input.
2. **Schema Strictness**: The "resumeData" object **MUST NOT** contain any keys or fields that are not present in the example above. Adhere strictly to the provided schema.
3. **Data Integrity**: Preserve the structure of the resume schema as shown in the example. The "skills" field must be an array of objects, each with a "name" (category) and "data" (list of skills). The "projects" field must use a "responsibilities" array for project details.
4. **Message Content**: The "message" field is for conversational feedback. It should be helpful and concise. Do not use markdown in the message. Use \\n for newlines if needed.
`,
].join("\n");

// =================================================================================
// 2. CREATE PROMPT (For generating a new resume)
// =================================================================================

const CREATE_PROMPT = [
  "## Task: Create New Resume",
  "The user wants to create a new resume. You are given their initial input and the target locale for the response.",
  "",
  "### Instructions",
  "1.  Analyze the user's input to extract as much information as possible.",
  "2.  Generate a complete `resumeData` object based on the extracted information, strictly following the system prompt's schema example.",
  '3.  If the user\'s input is sparse (e.g., "make me a resume"), create a reasonable, professional-looking draft.',
  "4.  Generate a friendly `message` in the specified locale. This message should welcome the user and, if you created a draft, suggest what information they should provide next to improve the resume.",
  "",
  "### Example",
  '- **User Input**: "I\'m a frontend developer with experience in React and TypeScript."',
  '- **Locale**: "en"',
  '- **Expected `message`**: "Welcome! I\'ve created a draft for your resume based on your role as a frontend developer. You can now add more details about your work experience, projects, and education."',
  "",
  "## User's Request",
  "- **Locale**: `{locale}`",
  "- **User Input**: `{userInput}`",
].join("\n");

// =================================================================================
// 3. UPDATE PROMPT (For modifying an existing resume)
// =================================================================================

const UPDATE_PROMPT = [
  "## Task: Update Existing Resume",
  "The user wants to modify their existing resume. You are given their current resume data (JSON), their modification request, and the target locale.",
  "",
  "### Instructions",
  "1.  Carefully analyze the user's request to understand the desired changes.",
  "2.  Apply these changes directly to the provided `currentResume` JSON data. **Do not** regenerate the entire resume.",
  "3.  Return the fully updated `resumeData` object, ensuring it still conforms to the system prompt's schema example.",
  "4.  Generate a concise `message` in the specified locale that **confirms what you have changed**. This is critical for user feedback.",
  "",
  "### Example",
  '- **User Request**: "add a new job at Google as a software engineer"',
  '- **Locale**: "zh"',
  '- **Expected `message`**: "好的，我已经将您在谷歌的软件工程师工作经历添加到了您的简历中。"',
  "",
  "## User's Request",
  "- **Locale**: `{locale}`",
  "- **User Input**: `{userInput}`",
  "- **Current Resume Data**:",
  "```json",
  "{currentResume}",
  "```",
].join("\n");

// =================================================================================
// 4. EXPORTED FUNCTION (Combines prompts for the AI call)
// =================================================================================

/**
 * Creates the final prompt for the AI by combining the system prompt
 * with the appropriate task-specific prompt (create or update).
 *
 * @param userInput The user\'s raw text input.
 * @param currentResume The user\'s current resume data, or null if creating a new one.
 * @param locale The target locale for the response message ('en' or 'zh').
 * @returns A single string containing the full prompt for the AI.
 */
export function createResumePrompt(
  userInput: string,
  currentResume: ResumeSchema | null,
  locale: string = "zh"
): string {
  const basePrompt = SYSTEM_PROMPT;
  let taskPrompt: string;

  if (currentResume) {
    // --- UPDATE FLOW ---
    taskPrompt = UPDATE_PROMPT.replace("{locale}", locale)
      .replace("{userInput}", userInput)
      .replace("{currentResume}", JSON.stringify(currentResume, null, 2));
  } else {
    // --- CREATE FLOW ---
    taskPrompt = CREATE_PROMPT.replace("{locale}", locale).replace(
      "{userInput}",
      userInput
    );
  }

  return `${basePrompt}\n\n${taskPrompt}`;
}

// =================================================================================
// 5. CHAT MODE PROMPTS (New conversation-based approach)
// =================================================================================

const CHAT_SYSTEM_PROMPT = [
  "## Role & Persona",
  "You are a professional and friendly AI Resume Assistant. You engage in natural conversations with users to help them create, update, and refine their professional resumes.",
  "",
  "## Conversation Context",
  "You are participating in an ongoing conversation. The user may reference previous messages, ask follow-up questions, or request specific changes to their resume.",
  "",
  "## Response Guidelines",
  "1. **Natural Conversation**: Respond naturally and conversationally, not as a single-shot completion.",
  "2. **Resume Updates**: Only update resume data when the user explicitly requests changes to their resume.",
  "3. **Token Efficiency**: When updating resume data, only include the changed fields, not the entire resume.",
  "4. **Context Awareness**: Consider the conversation history and any referenced conversations.",
  "",
  "## Output Format",
  "Your response should be in one of two formats:",
  "",
  "### Format 1: Regular Conversation (No Resume Changes)",
  "Just respond naturally with helpful advice, clarifications, or questions.",
  "",
  "### Format 2: Resume Update Required",
  "When the user requests resume changes, respond with:",
  "```json",
  "{",
  '  "resumeData": { /* Only the fields that need to be updated */ },',
  '  "message": "Your conversational response explaining the changes"',
  "}",
  "```",
  "",
  "## Current Resume Data",
  "The user's current resume data (if any) will be provided for context.",
  "",
  "## Referenced Conversations",
  "If the user references other conversations using @, those contexts will be provided.",
].join("\n");

/**
 * Creates a chat-mode prompt for conversational interactions
 *
 * @param conversationHistory Array of previous messages in the conversation
 * @param currentResumeData Current resume data (if any)
 * @param referencedContext Context from referenced conversations
 * @param locale Target locale for responses
 * @returns Formatted prompt for chat mode
 */
export function createChatPrompt(
  conversationHistory: Array<{ role: string; content: string }>,
  currentResumeData: ResumeSchema | null,
  referencedContext: Array<{ chatTitle: string; messages: any[] }> = [],
  locale: string = "zh"
): string {
  let prompt = CHAT_SYSTEM_PROMPT;

  // Add current resume data if available
  if (currentResumeData) {
    prompt +=
      "\n\n## Current Resume Data\n```json\n" +
      JSON.stringify(currentResumeData, null, 2) +
      "\n```";
  }

  // Add referenced conversation context
  if (referencedContext.length > 0) {
    prompt += "\n\n## Referenced Conversations\n";
    referencedContext.forEach((ref) => {
      prompt += `\n### Referenced Chat: "${ref.chatTitle}"\n`;
      ref.messages.forEach((msg) => {
        prompt += `**${msg.role}**: ${msg.content}\n`;
      });
    });
  }

  // Add conversation history
  if (conversationHistory.length > 0) {
    prompt += "\n\n## Conversation History\n";
    conversationHistory.forEach((msg) => {
      prompt += `**${msg.role}**: ${msg.content}\n`;
    });
  }

  // Add locale instruction
  prompt += `\n\n## Response Language\nPlease respond in ${
    locale === "zh" ? "Chinese" : "English"
  }.`;

  return prompt;
}
