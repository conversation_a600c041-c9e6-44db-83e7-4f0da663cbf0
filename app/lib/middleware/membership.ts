import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { checkMembershipStatus } from "@/app/lib/membership";

/**
 * 会员权限检查中间件
 */
export async function withMembershipCheck(
  request: NextRequest,
  handler: (request: NextRequest, membershipStatus: any) => Promise<NextResponse>
) {
  try {
    // 获取用户token
    const token = await getToken({ 
      req: request, 
      secret: process.env.NEXTAUTH_SECRET 
    });

    if (!token?.sub) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // 检查会员状态
    const membershipStatus = await checkMembershipStatus(token.sub);

    // 调用实际的处理函数
    return await handler(request, membershipStatus);
  } catch (error) {
    console.error("Membership check middleware error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * 要求会员权限的中间件
 */
export async function requireMembership(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return withMembershipCheck(request, async (req, membershipStatus) => {
    if (!membershipStatus.isMember) {
      return NextResponse.json(
        { 
          error: "Membership required",
          membershipRequired: true,
          membershipStatus 
        },
        { status: 403 }
      );
    }

    return await handler(req);
  });
}

/**
 * 检查聊天次数限制的中间件
 */
export async function checkChatLimit(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return withMembershipCheck(request, async (req, membershipStatus) => {
    if (!membershipStatus.canChat) {
      return NextResponse.json(
        { 
          error: "Chat limit exceeded",
          membershipRequired: true,
          chatCount: membershipStatus.chatCount,
          remainingChats: membershipStatus.remainingChats
        },
        { status: 403 }
      );
    }

    return await handler(req);
  });
}
