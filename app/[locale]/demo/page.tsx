"use client";

import { MessageContent } from "@/app/components/common/message-content";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function DemoPage() {
  const testMessages = [
    {
      title: "基础换行测试",
      content: "第一行文本\n第二行文本\n第三行文本"
    },
    {
      title: "空行测试", 
      content: "第一行\n\n第三行（上面有空行）\n\n\n第六行（上面有两个空行）"
    },
    {
      title: "复杂文本测试",
      content: `用户信息：
姓名：张三
职位：前端工程师

技能：
- React
- TypeScript
- Node.js

项目经验：
1. 电商平台开发
2. 管理系统开发
3. 移动端应用

联系方式：
邮箱：<EMAIL>
电话：138-0000-0000`
    }
  ];

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>换行解析演示</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {testMessages.map((msg, index) => (
              <div key={index} className="space-y-2">
                <h3 className="text-lg font-semibold text-gray-700">{msg.title}</h3>
                <div className="border rounded-lg p-4 bg-gray-50">
                  <div className="bg-white rounded-lg p-3 border">
                    <MessageContent content={msg.content} />
                  </div>
                </div>
                <details className="text-sm text-gray-600">
                  <summary className="cursor-pointer hover:text-gray-800">查看原始文本</summary>
                  <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                    {JSON.stringify(msg.content, null, 2)}
                  </pre>
                </details>
              </div>
            ))}
            
            <div className="mt-8 p-4 bg-blue-50 rounded-lg">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">功能说明</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>✅ 正确解析 \n 换行符</li>
                <li>✅ 保留空行显示</li>
                <li>✅ 维持原始文本格式</li>
                <li>✅ 支持复杂多行文本</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
