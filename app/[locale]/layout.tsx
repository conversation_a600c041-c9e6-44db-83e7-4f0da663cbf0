import type { Metada<PERSON> } from "next";
import { GeistSans } from "geist/font/sans";
import { GeistMono } from "geist/font/mono";
import "../globals.css";
import { NextIntlClientProvider } from "next-intl";
import { getMessages, setRequestLocale } from "next-intl/server";
import { AuthSessionProvider } from "../components/providers/session-provider";
import { ConfirmProvider } from "../providers/ConfirmProvider";
import { Toaster } from "@/components/ui/sonner"; // Import the new Toaster

export function generateStaticParams() {
  return [{ locale: "en" }, { locale: "zh" }];
}

export const metadata: Metadata = {
  title: "AI Resume Builder",
  description: "Build your resume with AI",
  icons: {
    icon: "/favicon.ico",
  },
};

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  const { locale } = await params;
  setRequestLocale(locale);
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body
        className={`${GeistSans.variable} ${GeistMono.variable} antialiased`}
      >
        <AuthSessionProvider>
          <NextIntlClientProvider messages={messages}>
            <ConfirmProvider>
              {children}
              <Toaster position="top-right" />
            </ConfirmProvider>
          </NextIntlClientProvider>
        </AuthSessionProvider>
      </body>
    </html>
  );
}
