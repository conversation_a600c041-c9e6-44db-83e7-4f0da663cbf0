import { StateCreator } from "zustand";
import { AppState, Chat, ChatMessage } from "../types";
import { ResumeSchema } from "../../lib/types";

export interface ChatSlice {
  // 当前对话状态
  currentChatId: string | null;
  currentChat: Chat | null;
  currentMessages: ChatMessage[];

  // 对话列表
  chatList: Chat[];
  isLoadingChats: boolean;

  // 对话操作
  setCurrentChatId: (chatId: string | null) => void;
  setCurrentChat: (chat: Chat | null) => void;
  setCurrentMessages: (messages: ChatMessage[]) => void;
  setChatList: (chats: Chat[]) => void;
  setIsLoadingChats: (loading: boolean) => void;

  // 对话管理方法
  loadChatList: () => Promise<void>;
  loadChat: (chatId: string) => Promise<void>;
  createNewChat: (
    title?: string,
    resumeData?: ResumeSchema
  ) => Promise<string | null>;
  updateChatTitle: (chatId: string, title: string) => Promise<boolean>;
  deleteChat: (chatId: string) => Promise<boolean>;

  // 消息管理方法
  addChatMessage: (
    message: Omit<ChatMessage, "id" | "chatId" | "createdAt">
  ) => Promise<void>;
  addCurrentChatMessage: (
    message: Omit<ChatMessage, "id" | "chatId" | "createdAt">
  ) => void;
  updateCurrentChatLastMessage: (content: string) => void;
  updateChatResumeData: (
    chatId: string,
    resumeData: ResumeSchema
  ) => Promise<boolean>;

  // 引用功能
  searchChatsForReference: (query: string) => Promise<Chat[]>;
  getChatContext: (
    chatId: string,
    messageCount?: number
  ) => Promise<ChatMessage[]>;

  // 标题生成
  generateChatTitle: (chatId: string) => Promise<boolean>;
}

export const createChatSlice: StateCreator<
  AppState,
  [["zustand/immer", never]],
  [],
  ChatSlice
> = (set, get) => ({
  // 初始状态
  currentChatId: null,
  currentChat: null,
  currentMessages: [],
  chatList: [],
  isLoadingChats: false,

  // 基础设置方法
  setCurrentChatId: (chatId) =>
    set((state) => {
      state.currentChatId = chatId;
    }),

  setCurrentChat: (chat) =>
    set((state) => {
      state.currentChat = chat;
    }),

  setCurrentMessages: (messages) =>
    set((state) => {
      state.currentMessages = messages;
    }),

  setChatList: (chats) =>
    set((state) => {
      state.chatList = chats;
    }),

  setIsLoadingChats: (loading) =>
    set((state) => {
      state.isLoadingChats = loading;
    }),

  // 加载对话列表
  loadChatList: async () => {
    set((state) => {
      state.isLoadingChats = true;
    });

    try {
      const response = await fetch("/api/chats");
      if (!response.ok) {
        throw new Error("Failed to load chats");
      }

      const { data: chats } = await response.json();

      set((state) => {
        state.chatList = chats;
        state.isLoadingChats = false;
      });
    } catch (error) {
      console.error("Failed to load chat list:", error);
      set((state) => {
        state.isLoadingChats = false;
      });
    }
  },

  // 加载特定对话
  loadChat: async (chatId: string) => {
    try {
      const response = await fetch(`/api/chats/${chatId}`);
      if (!response.ok) {
        throw new Error("Failed to load chat");
      }

      const { data: chat } = await response.json();

      set((state) => {
        state.currentChatId = chatId;
        state.currentChat = chat;
        state.currentMessages = chat.messages || [];

        // 更新简历数据到全局状态
        if (chat.resumeData) {
          state.resumeData = chat.resumeData;
        }
      });
    } catch (error) {
      console.error("Failed to load chat:", error);
    }
  },

  // 创建新对话
  createNewChat: async (title = "新对话", resumeData) => {
    try {
      const response = await fetch("/api/chats", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title,
          resumeData,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create chat");
      }

      const { data: chat } = await response.json();

      set((state) => {
        state.chatList.unshift(chat);
        state.currentChatId = chat.id;
        state.currentChat = chat;
        state.currentMessages = [];

        // 如果有简历数据，更新到全局状态
        if (resumeData) {
          state.resumeData = resumeData;
        }
      });

      return chat.id;
    } catch (error) {
      console.error("Failed to create chat:", error);
      return null;
    }
  },

  // 更新对话标题
  updateChatTitle: async (chatId: string, title: string) => {
    try {
      const response = await fetch(`/api/chats/${chatId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ title }),
      });

      if (!response.ok) {
        throw new Error("Failed to update chat title");
      }

      set((state) => {
        // 更新对话列表中的标题
        const chatIndex = state.chatList.findIndex(
          (chat) => chat.id === chatId
        );
        if (chatIndex !== -1) {
          state.chatList[chatIndex].title = title;
        }

        // 更新当前对话的标题
        if (state.currentChat?.id === chatId) {
          state.currentChat.title = title;
        }
      });

      return true;
    } catch (error) {
      console.error("Failed to update chat title:", error);
      return false;
    }
  },

  // 删除对话
  deleteChat: async (chatId: string) => {
    try {
      const response = await fetch(`/api/chats/${chatId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete chat");
      }

      set((state) => {
        const wasCurrentChat = state.currentChatId === chatId;

        // 从对话列表中移除
        state.chatList = state.chatList.filter((chat) => chat.id !== chatId);

        // 如果删除的是当前对话，尝试选择下一个对话
        if (wasCurrentChat) {
          if (state.chatList.length > 0) {
            // 选择第一个可用的对话
            const nextChat = state.chatList[0];
            state.currentChatId = nextChat.id;
            state.currentChat = nextChat;
            state.currentMessages = [];

            // 异步加载新对话的消息
            get().loadChat(nextChat.id);
          } else {
            // 没有其他对话了，清空状态
            state.currentChatId = null;
            state.currentChat = null;
            state.currentMessages = [];
          }
        }
      });

      return true;
    } catch (error) {
      console.error("Failed to delete chat:", error);
      return false;
    }
  },

  // 添加消息
  addChatMessage: async (message) => {
    const currentChatId = get().currentChatId;
    if (!currentChatId) {
      throw new Error("No current chat selected");
    }

    try {
      const response = await fetch(`/api/chats/${currentChatId}/messages`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(message),
      });

      if (!response.ok) {
        throw new Error("Failed to add message");
      }

      const { data: newMessage } = await response.json();

      set((state) => {
        state.currentMessages.push(newMessage);
      });
    } catch (error) {
      console.error("Failed to add message:", error);
    }
  },

  // 添加消息到当前对话（仅UI，不保存到数据库）
  addCurrentChatMessage: (message) =>
    set((state) => {
      const newMessage: ChatMessage = {
        id: `temp-${Date.now()}`,
        chatId: state.currentChatId || "",
        role: message.role,
        content: message.content,
        metadata: message.metadata,
        createdAt: new Date().toISOString(),
      };
      state.currentMessages.push(newMessage);
    }),

  // 更新当前对话的最后一条消息
  updateCurrentChatLastMessage: (content) =>
    set((state) => {
      if (state.currentMessages.length > 0) {
        const lastMessage =
          state.currentMessages[state.currentMessages.length - 1];
        if (lastMessage.role === "ASSISTANT") {
          lastMessage.content = content;
        }
      }
    }),

  // 更新对话的简历数据
  updateChatResumeData: async (chatId: string, resumeData: ResumeSchema) => {
    try {
      const response = await fetch(`/api/chats/${chatId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ resumeData }),
      });

      if (!response.ok) {
        throw new Error("Failed to update chat resume data");
      }

      set((state) => {
        // 更新当前对话的简历数据
        if (state.currentChat?.id === chatId) {
          state.currentChat.resumeData = resumeData;
        }

        // 更新全局简历数据
        state.resumeData = resumeData;
      });

      return true;
    } catch (error) {
      console.error("Failed to update chat resume data:", error);
      return false;
    }
  },

  // 搜索对话用于引用
  searchChatsForReference: async (query: string) => {
    try {
      const response = await fetch(
        `/api/chats/search?q=${encodeURIComponent(query)}`
      );
      if (!response.ok) {
        throw new Error("Failed to search chats");
      }

      const { data: chats } = await response.json();
      return chats;
    } catch (error) {
      console.error("Failed to search chats:", error);
      return [];
    }
  },

  // 获取对话上下文
  getChatContext: async (chatId: string, messageCount = 5) => {
    try {
      const response = await fetch(
        `/api/chats/${chatId}/context?limit=${messageCount}`
      );
      if (!response.ok) {
        throw new Error("Failed to get chat context");
      }

      const { data: messages } = await response.json();
      return messages;
    } catch (error) {
      console.error("Failed to get chat context:", error);
      return [];
    }
  },

  // 生成对话标题
  generateChatTitle: async (chatId: string) => {
    try {
      const response = await fetch(`/api/chats/${chatId}/generate-title`, {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error("Failed to generate chat title");
      }

      const { title } = await response.json();

      set((state) => {
        // 更新对话列表中的标题
        const chatIndex = state.chatList.findIndex(
          (chat) => chat.id === chatId
        );
        if (chatIndex !== -1) {
          state.chatList[chatIndex].title = title;
        }

        // 更新当前对话的标题
        if (state.currentChat?.id === chatId) {
          state.currentChat.title = title;
        }
      });

      return true;
    } catch (error) {
      console.error("Failed to generate chat title:", error);
      return false;
    }
  },
});
