import { StateCreator } from "zustand";
import { AppState, ResumeMetadata } from "../types";
import { ResumeSchema } from "@/app/lib/types";
import { defaultResumeData, emptyResumeData } from "../defaults";
import { SECTION_ORDER } from "./resume-layout-slice";

export interface ResumeManagementSlice {
  currentResumeId: string | null;
  resumeList: ResumeMetadata[];
  isLoadingResumes: boolean;
  setCurrentResumeId: (id: string | null) => void;
  setResumeList: (resumes: ResumeMetadata[]) => void;
  setIsLoadingResumes: (loading: boolean) => void;
  loadResumeList: () => Promise<void>;
  loadResume: (id: string) => Promise<void>;
  loadLatestResume: () => Promise<void>;
  initializeResumes: () => Promise<void>;
  createNewResume: () => void;
  isDefaultResumeData: () => boolean;
  saveResume: (
    name: string,
    resumeData: ResumeSchema
  ) => Promise<string | null>;
  saveAsResume: (
    name: string,
    resumeData: ResumeSchema
  ) => Promise<string | null>;
  updateCurrentResume: () => Promise<boolean>;
  deleteResume: (id: string) => Promise<boolean>;
}

export const createResumeManagementSlice: StateCreator<
  AppState,
  [["zustand/immer", never]],
  [],
  ResumeManagementSlice
> = (set, get) => ({
  currentResumeId: null,
  resumeList: [],
  isLoadingResumes: false,
  setCurrentResumeId: (id) =>
    set((state) => {
      state.currentResumeId = id;
    }),
  setResumeList: (resumes) =>
    set((state) => {
      state.resumeList = resumes;
    }),
  setIsLoadingResumes: (loading) =>
    set((state) => {
      state.isLoadingResumes = loading;
    }),
  loadResumeList: async () => {
    set((state) => {
      state.isLoadingResumes = true;
    });
    try {
      const response = await fetch("/api/resumes");
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          set((state) => {
            state.resumeList = result.data;
          });
        }
      }
    } catch (error) {
      console.error("Failed to load resume list:", error);
    } finally {
      set((state) => {
        state.isLoadingResumes = false;
      });
    }
  },
  loadResume: async (id) => {
    try {
      const response = await fetch(`/api/resumes/${id}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          const data = result.data.resumeData;
          set((state) => {
            if (data.resumeData) {
              state.resumeData = data.resumeData;
              state.sectionOrder = data.sectionOrder || [...SECTION_ORDER];
            } else {
              state.resumeData = data;
            }
            state.currentResumeId = id;
          });
        }
      }
    } catch (error) {
      console.error("Failed to load resume:", error);
    }
  },
  loadLatestResume: async () => {
    const currentState = get();
    let resumeList = currentState.resumeList;

    if (resumeList.length === 0) {
      await get().loadResumeList();
      resumeList = get().resumeList;
    }

    try {
      if (resumeList.length > 0) {
        const latestResume = resumeList[0];
        await get().loadResume(latestResume.id);
      } else {
        set({
          resumeData: defaultResumeData,
          currentResumeId: null,
          sectionOrder: [...SECTION_ORDER],
        });
      }
    } catch (error) {
      console.error("Failed to load latest resume:", error);
      set({
        resumeData: defaultResumeData,
        currentResumeId: null,
        sectionOrder: [...SECTION_ORDER],
      });
    }
  },
  initializeResumes: async () => {
    try {
      await get().loadResumeList();
      const state = get();
      if (!state.currentResumeId && state.resumeList.length > 0) {
        const latestResume = state.resumeList[0];
        await get().loadResume(latestResume.id);
      } else if (state.resumeList.length === 0) {
        set({
          resumeData: defaultResumeData,
          currentResumeId: null,
          sectionOrder: [...SECTION_ORDER],
        });
      }
    } catch (error) {
      console.error("Failed to initialize resumes:", error);
      set({
        resumeData: defaultResumeData,
        currentResumeId: null,
        sectionOrder: [...SECTION_ORDER],
      });
    }
  },
  createNewResume: () => {
    set({
      resumeData: emptyResumeData,
      currentResumeId: null,
      sectionOrder: [...SECTION_ORDER],
    });
  },
  isDefaultResumeData: () => {
    const state = get();
    if (!state.resumeData) return true;
    return (
      JSON.stringify(state.resumeData) === JSON.stringify(defaultResumeData)
    );
  },
  saveResume: async (name, resumeData) => {
    const state = get();
    try {
      const response = await fetch("/api/resumes", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name,
          resumeData,
          sectionOrder: state.sectionOrder,
          isDefault: false,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          set((state) => {
            state.resumeList = [result.data, ...state.resumeList];
            state.currentResumeId = result.data.id;
            return state; // Explicitly return state
          });
          return result.data.id;
        }
      }
      return null;
    } catch (error) {
      console.error("Failed to save resume:", error);
      return null;
    }
  },
  saveAsResume: async (name, resumeData) => {
    const state = get();
    try {
      const response = await fetch("/api/resumes", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name,
          resumeData,
          sectionOrder: state.sectionOrder,
          isDefault: false,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          set((state) => {
            state.resumeList = [result.data, ...state.resumeList];
            return state; // Explicitly return state
          });
          return result.data.id;
        }
      }
      return null;
    } catch (error) {
      console.error("Failed to save as resume:", error);
      return null;
    }
  },
  updateCurrentResume: async () => {
    const state = get();
    if (!state.currentResumeId || !state.resumeData) {
      return false;
    }

    try {
      const response = await fetch(`/api/resumes/${state.currentResumeId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          resumeData: state.resumeData,
          sectionOrder: state.sectionOrder,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          set((state) => {
            const index = state.resumeList.findIndex(
              (r) => r.id === state.currentResumeId
            );
            if (index !== -1) {
              state.resumeList[index] = {
                ...state.resumeList[index],
                updatedAt: new Date().toISOString(),
              };
              const updatedResume = state.resumeList.splice(index, 1)[0];
              state.resumeList.unshift(updatedResume);
            }
            return state; // Explicitly return state
          });
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error("Failed to update resume:", error);
      return false;
    }
  },
  deleteResume: async (id) => {
    try {
      const response = await fetch(`/api/resumes/${id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          const currentState = get();
          const isCurrentResume = currentState.currentResumeId === id;

          set((state) => {
            state.resumeList = state.resumeList.filter(
              (resume) => resume.id !== id
            );
            return state; // Explicitly return state
          });

          if (isCurrentResume) {
            const updatedState = get();
            if (updatedState.resumeList.length > 0) {
              const latestResume = updatedState.resumeList[0];
              await get().loadResume(latestResume.id);
            } else {
              set({
                currentResumeId: null,
                resumeData: defaultResumeData,
                sectionOrder: [...SECTION_ORDER],
              });
            }
          }

          return true;
        }
      }
      return false;
    } catch (error) {
      console.error("Failed to delete resume:", error);
      return false;
    }
  },
});
