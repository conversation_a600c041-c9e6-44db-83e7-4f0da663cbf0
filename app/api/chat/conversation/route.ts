import { generateText } from "ai";
import { aiConfig, validateAIConfig } from "@/app/lib/ai-config";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import {
  checkMembershipStatus,
  incrementChatCount,
} from "@/app/lib/membership";
import { createChatPrompt } from "@/app/lib/prompts";
import { prisma } from "@/lib/prisma";
import { ResumeSchema } from "@/app/lib/types";

// POST /api/chat/conversation - 新的对话式聊天API
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      chatId,
      userInput,
      locale = "zh",
      referencedChats = [], // 引用的其他对话
    } = body;

    if (!userInput) {
      return Response.json(
        { error: "User input is required" },
        { status: 400 }
      );
    }

    if (!chatId) {
      return Response.json({ error: "Chat ID is required" }, { status: 400 });
    }

    // 检查用户认证状态
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 检查会员状态和聊天次数限制
    const membershipStatus = await checkMembershipStatus(session.user.id);
    if (!membershipStatus.canChat) {
      return Response.json(
        {
          error: "Chat limit exceeded",
          membershipRequired: true,
          chatCount: membershipStatus.chatCount,
          remainingChats: membershipStatus.remainingChats,
        },
        { status: 403 }
      );
    }

    // 验证对话是否属于当前用户
    const chat = await prisma.chat.findUnique({
      where: {
        id: chatId,
        userId: session.user.id,
      },
      include: {
        messages: {
          orderBy: {
            createdAt: "asc",
          },
          take: 20, // 只取最近20条消息作为上下文
        },
      },
    });

    if (!chat) {
      return Response.json({ error: "Chat not found" }, { status: 404 });
    }

    // 获取引用对话的上下文
    let referencedContext = [];
    if (referencedChats.length > 0) {
      for (const refChatId of referencedChats) {
        const refChat = await prisma.chat.findUnique({
          where: {
            id: refChatId,
            userId: session.user.id,
          },
          include: {
            messages: {
              orderBy: {
                createdAt: "desc",
              },
              take: 5, // 每个引用对话取5条最新消息
            },
          },
        });

        if (refChat) {
          referencedContext.push({
            chatTitle: refChat.title,
            messages: refChat.messages.reverse(),
          });
        }
      }
    }

    // 先保存用户消息
    await prisma.chatMessage.create({
      data: {
        chatId,
        role: "USER",
        content: userInput,
        metadata: referencedChats.length > 0 ? { referencedChats } : undefined,
      },
    });

    // 验证AI配置
    validateAIConfig();

    // 构建对话历史
    const conversationHistory = chat.messages.map((msg) => ({
      role: msg.role.toLowerCase() as "user" | "assistant" | "system",
      content: msg.content,
    }));

    // 添加当前用户输入
    conversationHistory.push({
      role: "user",
      content: userInput,
    });

    // 创建AI提示词
    const prompt = createChatPrompt(
      conversationHistory,
      chat.resumeData as ResumeSchema | null,
      referencedContext,
      locale
    );

    // 使用Vercel AI SDK生成普通响应
    const result = await generateText({
      model: aiConfig.model,
      messages: [{ role: "system", content: prompt }],
      temperature: aiConfig.temperature,
      maxTokens: aiConfig.maxTokens,
    });

    const assistantMessage = result.text;

    // 保存AI回复
    await prisma.chatMessage.create({
      data: {
        chatId,
        role: "ASSISTANT",
        content: assistantMessage,
      },
    });

    // 解析AI回复，提取message和resumeData
    let responseMessage = assistantMessage;
    let updatedResumeData = null;

    try {
      // 尝试解析JSON格式的回复
      const jsonMatch = assistantMessage.match(/```json\n([\s\S]*?)\n```/);
      if (jsonMatch) {
        const parsedData = JSON.parse(jsonMatch[1]);
        if (parsedData.message) {
          responseMessage = parsedData.message;
        }
        if (parsedData.resumeData) {
          updatedResumeData = parsedData.resumeData;
          // 更新对话的简历数据
          await prisma.chat.update({
            where: { id: chatId },
            data: { resumeData: updatedResumeData },
          });
        }
      }
    } catch (error) {
      console.log("Failed to parse JSON response, using full text");
    }

    // 自动生成对话标题（在第3条消息后）
    try {
      const messageCount = await prisma.chatMessage.count({
        where: { chatId },
      });

      // 当对话达到4条消息时（2轮对话），且标题仍为默认值时，自动生成标题
      if (messageCount >= 4) {
        const currentChat = await prisma.chat.findUnique({
          where: { id: chatId },
          select: { title: true },
        });

        if (
          currentChat &&
          (currentChat.title === "新对话" ||
            currentChat.title.startsWith("新对话"))
        ) {
          // 异步生成标题，不阻塞响应
          fetch(
            `${
              process.env.NEXTAUTH_URL || "http://localhost:3000"
            }/api/chats/${chatId}/generate-title`,
            {
              method: "POST",
              headers: {
                Cookie: request.headers.get("Cookie") || "",
              },
            }
          ).catch((error) => {
            console.log("Failed to generate title:", error);
          });
        }
      }
    } catch (error) {
      console.log("Failed to check for title generation:", error);
    }

    // 增加用户聊天次数
    await incrementChatCount(session.user.id);

    // 更新对话的最后更新时间
    await prisma.chat.update({
      where: { id: chatId },
      data: { updatedAt: new Date() },
    });

    // 返回普通JSON响应
    return Response.json({
      success: true,
      message: responseMessage,
      resumeData: updatedResumeData,
    });
  } catch (error) {
    console.error("Conversation API Error:", error);

    if (error instanceof Error && error.message.includes("OPENAI_API_KEY")) {
      return Response.json(
        {
          error:
            "AI service is not configured. Please set up your OpenAI API key.",
        },
        { status: 500 }
      );
    }

    return Response.json(
      {
        error: "Internal Server Error",
      },
      { status: 500 }
    );
  }
}
