
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import {
  checkMembershipStatus,
  incrementChatCount,
} from "@/app/lib/membership";
import { generateResume } from "@/app/lib/ai-service";

// Non-streaming endpoint for backward compatibility
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { userInput, resumeData: currentResume, locale = "zh" } = body;

    if (!userInput) {
      return Response.json(
        { error: "User input is required" },
        { status: 400 }
      );
    }

    // 检查用户认证状态
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    // 检查会员状态和聊天次数限制
    const membershipStatus = await checkMembershipStatus(session.user.id);
    if (!membershipStatus.canChat) {
      return Response.json(
        {
          error: "Chat limit exceeded",
          membershipRequired: true,
          chatCount: membershipStatus.chatCount,
          remainingChats: membershipStatus.remainingChats,
        },
        { status: 403 }
      );
    }

    const result = await generateResume(userInput, currentResume, locale);

    // 增加用户聊天次数（只有在成功生成回复后才增加）
    await incrementChatCount(session.user.id);

    return Response.json(result);

  } catch (error) {
    console.error("API Error:", error);

    // Check if it's an API key error
    if (error instanceof Error && error.message.includes("OPENAI_API_KEY")) {
      return Response.json(
        {
          error:
            "AI service is not configured. Please set up your OpenAI API key.",
        },
        { status: 500 }
      );
    }

    return Response.json(
      {
        error: "Internal Server Error",
      },
      { status: 500 }
    );
  }
}

