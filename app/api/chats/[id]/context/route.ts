import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// GET /api/chats/[id]/context - 获取对话上下文用于引用
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: chatId } = await params;
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "5");

    // 验证对话是否属于当前用户
    const chat = await prisma.chat.findUnique({
      where: {
        id: chatId,
        userId: session.user.id,
      },
    });

    if (!chat) {
      return Response.json(
        { error: "Chat not found" },
        { status: 404 }
      );
    }

    // 获取最近的消息作为上下文
    const messages = await prisma.chatMessage.findMany({
      where: {
        chatId,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      select: {
        id: true,
        role: true,
        content: true,
        createdAt: true,
      },
    });

    // 反转顺序，使其按时间正序排列
    const orderedMessages = messages.reverse();

    return Response.json({
      success: true,
      data: orderedMessages,
    });
  } catch (error) {
    console.error("Failed to get chat context:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
