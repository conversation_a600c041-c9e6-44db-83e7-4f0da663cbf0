import { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// POST /api/chats/[id]/messages - 添加消息到对话
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return Response.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: chatId } = await params;
    const body = await request.json();
    const { role, content, metadata } = body;

    // 验证对话是否属于当前用户
    const chat = await prisma.chat.findUnique({
      where: {
        id: chatId,
        userId: session.user.id,
      },
    });

    if (!chat) {
      return Response.json(
        { error: "Chat not found" },
        { status: 404 }
      );
    }

    // 创建消息
    const message = await prisma.chatMessage.create({
      data: {
        chatId,
        role: role.toUpperCase(),
        content,
        metadata,
      },
    });

    // 更新对话的最后更新时间
    await prisma.chat.update({
      where: { id: chatId },
      data: { updatedAt: new Date() },
    });

    return Response.json({
      success: true,
      data: message,
    });
  } catch (error) {
    console.error("Failed to add message:", error);
    return Response.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
