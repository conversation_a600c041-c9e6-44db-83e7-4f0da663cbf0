'use client';

import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useTranslations } from 'next-intl';

interface AddButtonProps {
  onClick: (event: React.MouseEvent) => void;
}

export function AddButton({ onClick }: AddButtonProps) {
  const t = useTranslations('Buttons');
  return (
    <Button
      size="sm"
      variant="outline"
      className="h-6 w-6 p-0 bg-white/90 hover:bg-white shadow-sm"
      onClick={onClick}
      aria-label={t('add')}
    >
      <Plus className="h-3 w-3" />
    </Button>
  );
}
