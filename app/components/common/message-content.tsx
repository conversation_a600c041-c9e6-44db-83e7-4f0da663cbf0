import React from "react";

interface MessageContentProps {
  content: string;
  className?: string;
}

/**
 * MessageContent component handles proper line break parsing and text formatting
 * for chat messages and other text content that needs to preserve line breaks.
 */
export function MessageContent({
  content,
  className = "",
}: MessageContentProps) {
  // Handle different types of line breaks and preserve formatting
  const processContent = (text: string) => {
    if (!text) return null;

    // Split by various line break patterns (\n, \r\n, \r)
    const lines = text.split(/\r?\n/);

    return lines.map((line, index) => {
      // Handle empty lines - preserve them with proper spacing
      if (line.trim() === "") {
        return (
          <div key={index} className="h-5 leading-5">
            {"\u00A0"} {/* Non-breaking space for empty lines */}
          </div>
        );
      }

      // Regular line with content
      return (
        <div key={index} className="leading-5">
          {line}
        </div>
      );
    });
  };

  return (
    <div className={`text-sm ${className}`}>{processContent(content)}</div>
  );
}

/**
 * Enhanced MessageContent component with additional formatting options
 */
export function EnhancedMessageContent({
  content,
  className = "",
  preserveWhitespace = false,
  enableMarkdown = false,
}: MessageContentProps & {
  preserveWhitespace?: boolean;
  enableMarkdown?: boolean;
}) {
  const processContent = (text: string) => {
    if (!text) return null;

    // Split by various line break patterns
    const lines = text.split(/\r?\n/);

    return lines.map((line, index) => {
      // Handle empty lines
      if (line.trim() === "") {
        return (
          <div key={index} className="h-5 leading-5">
            {"\u00A0"}
          </div>
        );
      }

      // Process line content based on options
      let processedLine = line;

      // Preserve whitespace if enabled
      if (preserveWhitespace) {
        processedLine = line.replace(/ /g, "\u00A0");
      }

      // Basic markdown support (if enabled)
      if (enableMarkdown) {
        // Bold text: **text** or __text__
        processedLine = processedLine.replace(
          /\*\*(.*?)\*\*/g,
          "<strong>$1</strong>"
        );
        processedLine = processedLine.replace(
          /__(.*?)__/g,
          "<strong>$1</strong>"
        );

        // Italic text: *text* or _text_
        processedLine = processedLine.replace(/\*(.*?)\*/g, "<em>$1</em>");
        processedLine = processedLine.replace(/_(.*?)_/g, "<em>$1</em>");

        // Code: `code`
        processedLine = processedLine.replace(
          /`(.*?)`/g,
          '<code class="bg-gray-100 px-1 rounded text-xs">$1</code>'
        );
      }

      if (enableMarkdown) {
        return (
          <div
            key={index}
            className="leading-5"
            dangerouslySetInnerHTML={{ __html: processedLine }}
          />
        );
      } else {
        return (
          <div key={index} className="leading-5">
            {processedLine}
          </div>
        );
      }
    });
  };

  return (
    <div
      className={`text-sm ${className} ${
        preserveWhitespace ? "font-mono" : ""
      }`}
    >
      {processContent(content)}
    </div>
  );
}

export default MessageContent;
