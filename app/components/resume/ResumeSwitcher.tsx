"use client";

import { useState, useEffect } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { ChevronDown, FileText, Plus, Loader2, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useAppStore } from "@/app/store";
import { ResumeNameDialog } from "./ResumeNameDialog";
import { ResumeDeleteDialog } from "./ResumeDeleteDialog";
import type { ResumeMetadata } from "@/app/store/types";

export function ResumeSwitcher() {
  const t = useTranslations("ResumeSwitcher");
  const {
    currentResumeId,
    resumeList,
    isLoadingResumes,
    resumeData,
    loadResumeList,
    loadResume,
    initializeResumes,
    createNewResume,
    saveResume,
    saveAsResume,
    deleteResume,
  } = useAppStore();

  const [isNameDialogOpen, setIsNameDialogOpen] = useState(false);
  const [isSaveAsDialogOpen, setIsSaveAsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [resumeToDelete, setResumeToDelete] = useState<ResumeMetadata | null>(
    null
  );

  // 获取当前简历信息
  const currentResume = resumeList.find(
    (r: ResumeMetadata) => r.id === currentResumeId
  );
  const displayName = currentResume?.name || t("currentResume");

  // 组件挂载时初始化简历数据
  useEffect(() => {
    // 使用统一的初始化方法，避免重复请求
    initializeResumes();
    // 只在组件挂载时执行一次
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSwitchResume = async (resumeId: string) => {
    if (resumeId !== currentResumeId) {
      await loadResume(resumeId);
    }
  };

  const handleNewResume = () => {
    // 创建空的新简历
    createNewResume();
    setIsNameDialogOpen(true);
  };

  const handleSaveNewResume = async (name: string) => {
    if (resumeData) {
      const savedId = await saveResume(name, resumeData);
      if (savedId) {
        // 刷新简历列表
        await loadResumeList();
      }
    }
  };

  const handleSaveAsConfirm = async (name: string) => {
    if (resumeData) {
      const savedId = await saveAsResume(name, resumeData);
      if (savedId) {
        // 刷新简历列表
        await loadResumeList();
      }
    }
  };

  const handleDelete = (resume: ResumeMetadata) => {
    setResumeToDelete(resume);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (resumeToDelete) {
      // deleteResume 方法现在已经处理了自动选择逻辑
      await deleteResume(resumeToDelete.id);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            <FileText className="h-4 w-4" />
            <span className="max-w-[120px] truncate">{displayName}</span>
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-56">
          <DropdownMenuLabel>{t("switchResume")}</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {isLoadingResumes ? (
            <DropdownMenuItem disabled>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              {t("loading")}
            </DropdownMenuItem>
          ) : resumeList.length === 0 ? (
            <DropdownMenuItem disabled>{t("noResumes")}</DropdownMenuItem>
          ) : (
            resumeList.map((resume: ResumeMetadata) => (
              <div key={resume.id} className="group">
                <DropdownMenuItem
                  onClick={() => handleSwitchResume(resume.id)}
                  className={`pr-2 ${
                    resume.id === currentResumeId
                      ? "bg-accent text-accent-foreground"
                      : ""
                  }`}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  <div className="flex-1 min-w-0">
                    <div className="truncate">{resume.name}</div>
                    {resume.isDefault && (
                      <div className="text-xs text-muted-foreground">默认</div>
                    )}
                  </div>
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    {/* <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSaveAs();
                      }}
                      title={t("saveAs")}
                    >
                      <Copy className="h-3 w-3" />
                    </Button> */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(resume);
                      }}
                      title={t("delete")}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </DropdownMenuItem>
              </div>
            ))
          )}

          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleNewResume}>
            <Plus className="h-4 w-4 mr-2" />
            {t("newResume")}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <ResumeNameDialog
        open={isNameDialogOpen}
        onOpenChange={setIsNameDialogOpen}
        onConfirm={handleSaveNewResume}
        title="新建简历"
      />

      <ResumeNameDialog
        open={isSaveAsDialogOpen}
        onOpenChange={setIsSaveAsDialogOpen}
        onConfirm={handleSaveAsConfirm}
        title="另存为"
        defaultName={currentResume?.name ? `${currentResume.name} - 副本` : ""}
      />

      <ResumeDeleteDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        resume={resumeToDelete}
      />
    </>
  );
}
